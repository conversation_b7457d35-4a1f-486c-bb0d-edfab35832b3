/**
 * AgentPanel - Critical Task List UI implementing Gemini's three principles
 * 
 * State Machine:
 * 1. Awaiting Approval - Shows plan with rationale and approval buttons
 * 2. Executing - Shows real-time task progress with activity updates
 * 3. Complete/Failed - Shows results with suggested next actions
 * 
 * Implements:
 * - Principle 1: Execution Rationale (plan explanation)
 * - Principle 2: Agent Activity Instrumentation (real-time updates)
 * - Principle 3: Post-Execution User Actions (suggested next steps)
 */

import React, { useState, useEffect } from 'react';
import { useProjectStore } from '../../stores/projectStore';
import { <PERSON>ton, Card, CardHeader, CardTitle, CardContent, Badge, LoadingSpinner } from '../ui';
import { TaskItem } from './TaskItem';
import { SuggestedActions } from './SuggestedActions';
import { BrainstormingInterface } from './BrainstormingInterface';
import { CheckCircle, XCircle, Clock, Play, AlertCircle } from 'lucide-react';

export const AgentPanel: React.FC = () => {
  const [goalInput, setGoalInput] = useState('');
  const [showGoalInput, setShowGoalInput] = useState(false);

  const {
    projectState,
    currentPlan,
    isExecuting,
    executionResult,
    isConnected,
    error,
    startBrainstorming,
    approvePlan,
    clearError,
  } = useProjectStore();

  const handleSubmitGoal = async () => {
    if (goalInput.trim()) {
      // Start brainstorming instead of jumping to task execution
      await startBrainstorming(goalInput.trim());
      setGoalInput('');
      setShowGoalInput(false);
    }
  };

  const handleApprovePlan = () => {
    approvePlan(true);
  };

  const handleRejectPlan = () => {
    approvePlan(false);
  };

  // Listen for custom events from SuggestedActions
  useEffect(() => {
    const handleStartNewGoal = () => {
      setShowGoalInput(true);
      setGoalInput('');
    };

    const handleEditGoalReplan = (event: CustomEvent) => {
      setShowGoalInput(true);
      setGoalInput(event.detail?.lastGoal || '');
    };

    window.addEventListener('start-new-goal', handleStartNewGoal);
    window.addEventListener('edit-goal-replan', handleEditGoalReplan as EventListener);

    return () => {
      window.removeEventListener('start-new-goal', handleStartNewGoal);
      window.removeEventListener('edit-goal-replan', handleEditGoalReplan as EventListener);
    };
  }, []);

  // Connection status indicator
  const ConnectionStatus = () => (
    <div className="flex items-center space-x-2 text-sm">
      <div className={`h-2 w-2 rounded-full ${isConnected ? 'bg-success-500' : 'bg-error-500'}`} />
      <span className={isConnected ? 'text-success-700' : 'text-error-700'}>
        {isConnected ? 'Connected' : 'Disconnected'}
      </span>
    </div>
  );

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Task Execution Agent</h2>
          <ConnectionStatus />
        </div>

        {/* Error display */}
        {error && (
          <div className="mt-3 rounded-md bg-error-50 p-3">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-error-400" />
              <div className="ml-3">
                <p className="text-sm text-error-800">{error}</p>
                <button
                  onClick={clearError}
                  className="mt-1 text-sm text-error-600 hover:text-error-500"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main content area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">

        {/* State 1: Awaiting Approval */}
        {currentPlan && !isExecuting && !executionResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-warning-500" />
                <span>Plan Awaiting Approval</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Principle 1: Execution Rationale */}
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Goal</h4>
                <p className="text-sm text-gray-600 mb-3">{currentPlan.goal}</p>

                <h4 className="font-medium text-gray-900 mb-2">Execution Rationale</h4>
                <p className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
                  {currentPlan.rationale}
                </p>
              </div>

              {/* Task list preview */}
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  Planned Tasks ({currentPlan.tasks.length})
                </h4>
                <div className="space-y-2">
                  {currentPlan.tasks.map((task, index) => (
                    <div key={task.id} className="flex items-center space-x-3 text-sm">
                      <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 text-gray-600">
                        {index + 1}
                      </span>
                      <span className="text-gray-700">{task.description}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Approval buttons */}
              <div className="flex space-x-3 pt-4">
                <Button onClick={handleApprovePlan} className="flex-1">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve Plan
                </Button>
                <Button variant="outline" onClick={handleRejectPlan} className="flex-1">
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject Plan
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* State 2: Executing */}
        {isExecuting && currentPlan && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <LoadingSpinner size="sm" />
                <span>Executing Tasks</span>
                <Badge variant="info">In Progress</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {currentPlan.tasks.map((task) => (
                  <TaskItem key={task.id} task={task} />
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* State 3: Complete/Failed */}
        {executionResult && !isExecuting && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {executionResult.success ? (
                  <>
                    <CheckCircle className="h-5 w-5 text-success-500" />
                    <span>Execution Complete</span>
                    <Badge variant="success">Success</Badge>
                  </>
                ) : (
                  <>
                    <XCircle className="h-5 w-5 text-error-500" />
                    <span>Execution Failed</span>
                    <Badge variant="error">Failed</Badge>
                  </>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Summary */}
              <div>
                <p className="text-sm text-gray-600">{executionResult.summary_message}</p>
                <p className="text-xs text-gray-500 mt-1">
                  Duration: {executionResult.total_duration_seconds.toFixed(1)}s
                </p>
              </div>

              {/* Failed task details */}
              {executionResult.failed_task && (
                <div className="rounded-md bg-error-50 p-3">
                  <h4 className="font-medium text-error-900 mb-1">Failed Task</h4>
                  <p className="text-sm text-error-800">{executionResult.failed_task.description}</p>
                  {executionResult.failed_task.error_message && (
                    <p className="text-xs text-error-600 mt-1">
                      {executionResult.failed_task.error_message}
                    </p>
                  )}
                </div>
              )}

              {/* Completed tasks summary */}
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  Completed Tasks ({executionResult.completed_tasks.length})
                </h4>
                <div className="space-y-1">
                  {executionResult.completed_tasks.map((task) => (
                    <div key={task.id} className="flex items-center space-x-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-success-500" />
                      <span className="text-gray-700">{task.description}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Principle 3: Post-Execution User Actions */}
              <SuggestedActions actions={executionResult.suggested_actions} />
            </CardContent>
          </Card>
        )}

        {/* Brainstorming state */}
        {projectState?.status === 'brainstorming' && projectState.brainstorming_session && (
          <BrainstormingInterface sessionId={projectState.session_id} />
        )}

        {/* Initial state - Project Start Here */}
        {!currentPlan && !isExecuting && !executionResult &&
          (!projectState || projectState.status !== 'brainstorming') && (
            <Card>
              <CardHeader>
                <CardTitle>Project Start Here</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Welcome to CodeQuilter! Describe what you want to build and I'll guide you through an intelligent brainstorming session to understand your requirements.
                </p>

                {!showGoalInput ? (
                  <Button onClick={() => setShowGoalInput(true)} className="w-full">
                    <Play className="mr-2 h-4 w-4" />
                    Start New Project
                  </Button>
                ) : (
                  <div className="space-y-3">
                    <textarea
                      value={goalInput}
                      onChange={(e) => setGoalInput(e.target.value)}
                      placeholder="e.g., I need a simple app to track my small business inventory and send me alerts when items are running low"
                      className="w-full rounded-md border border-gray-300 p-3 text-sm resize-none"
                      rows={3}
                      autoFocus
                    />
                    <div className="flex space-x-2">
                      <Button onClick={handleSubmitGoal} disabled={!goalInput.trim()}>
                        Start Brainstorming
                      </Button>
                      <Button variant="outline" onClick={() => setShowGoalInput(false)}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
      </div>
    </div>
  );
};
